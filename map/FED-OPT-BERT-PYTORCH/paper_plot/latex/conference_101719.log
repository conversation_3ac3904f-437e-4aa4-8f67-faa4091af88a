This is pdfTeX, Version 3.141592653-2.6-1.40.22 (TeX Live 2022/dev/Debian) (preloaded format=pdflatex 2025.8.30)  30 AUG 2025 12:06
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**conference_101719.tex
(./conference_101719.tex
LaTeX2e <2021-11-15> patch level 1
L3 programming layer <2022-01-21>
(/usr/share/texlive/texmf-dist/tex/latex/ieeetran/IEEEtran.cls
Document Class: IEEEtran 2015/08/26 V1.8b by <PERSON>
-- See the "IEEEtran_HOWTO" manual for usage information.
-- http://www.michaelshell.org/tex/ieeetran/
\@IEEEtrantmpdimenA=\dimen138
\@IEEEtrantmpdimenB=\dimen139
\@IEEEtrantmpdimenC=\dimen140
\@IEEEtrantmpcountA=\count185
\@IEEEtrantmpcountB=\count186
\@IEEEtrantmpcountC=\count187
\@IEEEtrantmptoksA=\toks16
LaTeX Font Info:    Trying to load font information for OT1+ptm on input line 5
03.
(/usr/share/texlive/texmf-dist/tex/latex/psnfss/ot1ptm.fd
File: ot1ptm.fd 2001/06/04 font definitions for OT1/ptm.
)
-- Using 8.5in x 11in (letter) paper.
-- Using PDF output.
\@IEEEnormalsizeunitybaselineskip=\dimen141
-- This is a 10 point document.
\CLASSINFOnormalsizebaselineskip=\dimen142
\CLASSINFOnormalsizeunitybaselineskip=\dimen143
\IEEEnormaljot=\dimen144
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <5> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <5> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <7> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <7> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <8> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <8> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <9> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <9> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <10> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <10> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <11> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <11> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <12> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <12> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <17> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <17> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <20> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <20> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <24> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <24> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

\IEEEquantizedlength=\dimen145
\IEEEquantizedlengthdiff=\dimen146
\IEEEquantizedtextheightdiff=\dimen147
\IEEEilabelindentA=\dimen148
\IEEEilabelindentB=\dimen149
\IEEEilabelindent=\dimen150
\IEEEelabelindent=\dimen151
\IEEEdlabelindent=\dimen152
\IEEElabelindent=\dimen153
\IEEEiednormlabelsep=\dimen154
\IEEEiedmathlabelsep=\dimen155
\IEEEiedtopsep=\skip47
\c@section=\count188
\c@subsection=\count189
\c@subsubsection=\count190
\c@paragraph=\count191
\c@IEEEsubequation=\count192
\abovecaptionskip=\skip48
\belowcaptionskip=\skip49
\c@figure=\count193
\c@table=\count194
\@IEEEeqnnumcols=\count195
\@IEEEeqncolcnt=\count196
\@IEEEsubeqnnumrollback=\count197
\@IEEEquantizeheightA=\dimen156
\@IEEEquantizeheightB=\dimen157
\@IEEEquantizeheightC=\dimen158
\@IEEEquantizeprevdepth=\dimen159
\@IEEEquantizemultiple=\count198
\@IEEEquantizeboxA=\box50
\@IEEEtmpitemindent=\dimen160
\IEEEPARstartletwidth=\dimen161
\c@IEEEbiography=\count199
\@IEEEtranrubishbin=\box51
)
** ATTENTION: Overriding command lockouts (line 2).
(/usr/share/texlive/texmf-dist/tex/latex/cite/cite.sty
LaTeX Info: Redefining \cite on input line 302.
LaTeX Info: Redefining \nocite on input line 332.
Package: cite 2015/02/27  v 5.5
)
(/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2021/10/15 v2.17l AMS math features
\@mathmargin=\skip50

For additional information on amsmath, use the `?' option.
(/usr/share/texlive/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text

(/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks17
\ex@=\dimen162
))
(/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen163
)
(/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2021/08/26 v2.02 operator names
)
\inf@bad=\count266
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count267
\leftroot@=\count268
LaTeX Info: Redefining \overline on input line 399.
\classnum@=\count269
\DOTSCASE@=\count270
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box52
\strutbox@=\box53
\big@size=\dimen164
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count271
\c@MaxMatrixCols=\count272
\dotsspace@=\muskip16
\c@parentequation=\count273
\dspbrk@lvl=\count274
\tag@help=\toks18
\row@=\count275
\column@=\count276
\maxfields@=\count277
\andhelp@=\toks19
\eqnshift@=\dimen165
\alignsep@=\dimen166
\tagshift@=\dimen167
\tagwidth@=\dimen168
\totwidth@=\dimen169
\lineht@=\dimen170
\@envbody=\toks20
\multlinegap=\skip51
\multlinetaggap=\skip52
\mathdisplay@stack=\toks21
LaTeX Info: Redefining \[ on input line 2938.
LaTeX Info: Redefining \] on input line 2939.
)
(/usr/share/texlive/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols

(/usr/share/texlive/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
))
(/usr/share/texlive/texmf-dist/tex/latex/algorithms/algorithmic.sty
Package: algorithmic 2009/08/24 v0.1 Document Style `algorithmic'

(/usr/share/texlive/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2020/11/24 v1.1c Standard LaTeX ifthen package (DPC)
)
(/usr/share/texlive/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2014/10/28 v1.15 key=value parser (DPC)
\KV@toks@=\toks22
)
\c@ALC@unique=\count278
\c@ALC@line=\count279
\c@ALC@rem=\count280
\c@ALC@depth=\count281
\ALC@tlm=\skip53
\algorithmicindent=\skip54
)
(/usr/share/texlive/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(/usr/share/texlive/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2021/03/04 v1.4d Standard LaTeX Graphics (DPC,SPQR)

(/usr/share/texlive/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
)
(/usr/share/texlive/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 107.

(/usr/share/texlive/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2020/10/05 v1.2a Graphics/color driver for pdftex
))
\Gin@req@height=\dimen171
\Gin@req@width=\dimen172
)
(/usr/share/texlive/texmf-dist/tex/latex/base/textcomp.sty
Package: textcomp 2020/02/02 v2.0n Standard LaTeX package
)
(/usr/share/texlive/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2021/10/31 v2.13 LaTeX color extensions (UK)

(/usr/share/texlive/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 227.
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1352.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1356.
Package xcolor Info: Model `RGB' extended on input line 1368.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1370.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1371.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1372.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1373.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1374.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1375.
)
(/usr/share/texlive/texmf-dist/tex/latex/float/float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count282
\float@exts=\toks23
\float@box=\box54
\@float@everytoks=\toks24
\@floatcapt=\box55
)
(/usr/share/texlive/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2022-01-12 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count283
\l__pdf_internal_box=\box56
)
(./conference_101719.aux)
\openout1 = `conference_101719.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.

-- Lines per column: 56 (exact).
(/usr/share/texlive/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count284
\scratchdimen=\dimen173
\scratchbox=\box57
\nofMPsegments=\count285
\nofMParguments=\count286
\everyMPshowfont=\toks25
\MPscratchCnt=\count287
\MPscratchDim=\dimen174
\MPnumerator=\count288
\makeMPintoPDFobject=\count289
\everyMPtoPDFconversion=\toks26
) (/usr/share/texlive/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 4
85.

(/usr/share/texlive/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Liv
e
))
LaTeX Font Info:    Trying to load font information for U+msa on input line 58.


(/usr/share/texlive/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 58.


(/usr/share/texlive/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
LaTeX Font Info:    Calculating math sizes for size <11> on input line 58.
 [1{/var/lib/texmf/fonts/map/pdftex/updmap/pdftex.map}


]
LaTeX Font Info:    Trying to load font information for OT1+pcr on input line 1
28.

(/usr/share/texlive/texmf-dist/tex/latex/psnfss/ot1pcr.fd
File: ot1pcr.fd 2001/06/04 font definitions for OT1/pcr.
) [2]
Overfull \hbox (9.58073pt too wide) in paragraph at lines 231--246
 [][] 
 []


Overfull \hbox (9.58073pt too wide) in paragraph at lines 250--265
 [][] 
 []


Overfull \hbox (9.58073pt too wide) in paragraph at lines 276--291
 [][] 
 []


Overfull \hbox (9.58073pt too wide) in paragraph at lines 295--310
 [][] 
 []


Underfull \hbox (badness 10000) in paragraph at lines 327--328
[]\OT1/ptm/b/n/10 Model Spec-i-fi-ca-tions: \OT1/ptm/m/n/10 BART-large uses
 []


Underfull \hbox (badness 1448) in paragraph at lines 327--328
\OT1/ptm/m/n/10 Dis-til-BART uses the dis-tilled vari-ant (66M pa-ram-e-ters).
 []


Underfull \hbox (badness 10000) in paragraph at lines 327--328
\OT1/ptm/m/n/10 Both mod-els pro-cess CNN/DailyMail dataset with
 []


Underfull \hbox (badness 1360) in paragraph at lines 327--328
\OT1/ptm/m/n/10 train/validation/test splits, us-ing null sam-pling lim-its for

 []


Underfull \hbox (badness 2538) in paragraph at lines 346--347
 \OT1/ptm/m/it/10 b) Find-ings.: [][] \OT1/ptm/m/n/10 Our clas-si-fi-ca-tion ex
-per-i-ments on 20
 []

[3]
<../plots/classification/performance_comparison_accuracy.png, id=22, 709.6914pt
 x 348.3414pt>
File: ../plots/classification/performance_comparison_accuracy.png Graphic file 
(type png)
<use ../plots/classification/performance_comparison_accuracy.png>
Package pdftex.def Info: ../plots/classification/performance_comparison_accurac
y.png  used on input line 353.
(pdftex.def)             Requested size: 252.0pt x 123.68597pt.
<../plots/classification/performance_comparison_f1.png, id=24, 709.6914pt x 348
.3414pt>
File: ../plots/classification/performance_comparison_f1.png Graphic file (type 
png)
<use ../plots/classification/performance_comparison_f1.png>
Package pdftex.def Info: ../plots/classification/performance_comparison_f1.png 
 used on input line 361.
(pdftex.def)             Requested size: 252.0pt x 123.68597pt.
<../plots/classification/fed_vs_central_progress.png, id=25, 854.2314pt x 276.0
714pt>
File: ../plots/classification/fed_vs_central_progress.png Graphic file (type pn
g)
<use ../plots/classification/fed_vs_central_progress.png>
Package pdftex.def Info: ../plots/classification/fed_vs_central_progress.png  u
sed on input line 369.
(pdftex.def)             Requested size: 252.0pt x 81.44035pt.

Overfull \hbox (16.98851pt too wide) in paragraph at lines 380--388
 [][] 
 []


Underfull \vbox (badness 10000) has occurred while \output is active []


Underfull \vbox (badness 10000) has occurred while \output is active []

 [4 <../plots/classification/performance_comparison_accuracy.png>]
<../plots/generation/performance_comparison_rouge1.png, id=33, 709.6914pt x 348
.3414pt>
File: ../plots/generation/performance_comparison_rouge1.png Graphic file (type 
png)
<use ../plots/generation/performance_comparison_rouge1.png>
Package pdftex.def Info: ../plots/generation/performance_comparison_rouge1.png 
 used on input line 459.
(pdftex.def)             Requested size: 247.6778pt x 121.56519pt.
<../plots/generation/performance_comparison_bleu4.png, id=34, 709.6914pt x 348.
3414pt>
File: ../plots/generation/performance_comparison_bleu4.png Graphic file (type p
ng)
<use ../plots/generation/performance_comparison_bleu4.png>
Package pdftex.def Info: ../plots/generation/performance_comparison_bleu4.png  
used on input line 464.
(pdftex.def)             Requested size: 247.6778pt x 121.56519pt.
<../plots/classification/cls_f1_vs_clients_combined.png, id=35, 637.4214pt x 34
8.3414pt>
File: ../plots/classification/cls_f1_vs_clients_combined.png Graphic file (type
 png)
<use ../plots/classification/cls_f1_vs_clients_combined.png>
Package pdftex.def Info: ../plots/classification/cls_f1_vs_clients_combined.png
  used on input line 496.
(pdftex.def)             Requested size: 252.0pt x 137.71294pt.

Underfull \hbox (badness 1694) in paragraph at lines 508--509
 \OT1/ptm/m/it/10 b) Find-ings.: [][] \OT1/ptm/m/n/10 Text gen-er-a-tion re-sul
ts re-veal dis-tinct
 []

[5 <../plots/classification/performance_comparison_f1.png> <../plots/classifica
tion/fed_vs_central_progress.png> <../plots/generation/performance_comparison_r
ouge1.png> <../plots/generation/performance_comparison_bleu4.png> <../plots/cla
ssification/cls_f1_vs_clients_combined.png>]
<../plots/generation/rouge1_vs_clients_combined.png, id=46, 637.4214pt x 348.34
14pt>
File: ../plots/generation/rouge1_vs_clients_combined.png Graphic file (type png
)
<use ../plots/generation/rouge1_vs_clients_combined.png>
Package pdftex.def Info: ../plots/generation/rouge1_vs_clients_combined.png  us
ed on input line 514.
(pdftex.def)             Requested size: 252.0pt x 137.71294pt.
File: ../plots/classification/cls_f1_vs_clients_combined.png Graphic file (type
 png)
<use ../plots/classification/cls_f1_vs_clients_combined.png>
Package pdftex.def Info: ../plots/classification/cls_f1_vs_clients_combined.png
  used on input line 532.
(pdftex.def)             Requested size: 252.0pt x 137.71294pt.
File: ../plots/generation/rouge1_vs_clients_combined.png Graphic file (type png
)
<use ../plots/generation/rouge1_vs_clients_combined.png>
Package pdftex.def Info: ../plots/generation/rouge1_vs_clients_combined.png  us
ed on input line 544.
(pdftex.def)             Requested size: 247.6778pt x 135.35295pt.
<../plots/generation/bleu4_vs_clients_combined.png, id=47, 637.1805pt x 348.341
4pt>
File: ../plots/generation/bleu4_vs_clients_combined.png Graphic file (type png)

<use ../plots/generation/bleu4_vs_clients_combined.png>
Package pdftex.def Info: ../plots/generation/bleu4_vs_clients_combined.png  use
d on input line 549.
(pdftex.def)             Requested size: 247.6778pt x 135.4008pt.
 [6 <../plots/generation/rouge1_vs_clients_combined.png>]
<../analysis_results/classification/federated/client_distributions/overall_clas
s_distribution.png, id=52, 1076.823pt x 569.9694pt>
File: ../analysis_results/classification/federated/client_distributions/overall
_class_distribution.png Graphic file (type png)
<use ../analysis_results/classification/federated/client_distributions/overall_
class_distribution.png>
Package pdftex.def Info: ../analysis_results/classification/federated/client_di
stributions/overall_class_distribution.png  used on input line 569.
(pdftex.def)             Requested size: 252.0pt x 133.37752pt.
<../analysis_results/classification/federated/client_distributions/client_share
_stacked_2_5.png, id=53, 860.013pt x 425.4294pt>
File: ../analysis_results/classification/federated/client_distributions/client_
share_stacked_2_5.png Graphic file (type png)
<use ../analysis_results/classification/federated/client_distributions/client_s
hare_stacked_2_5.png>
Package pdftex.def Info: ../analysis_results/classification/federated/client_di
stributions/client_share_stacked_2_5.png  used on input line 579.
(pdftex.def)             Requested size: 247.6778pt x 122.52097pt.
<../analysis_results/classification/federated/client_distributions/client_share
_stacked_2_10.png, id=54, 860.013pt x 425.4294pt>
File: ../analysis_results/classification/federated/client_distributions/client_
share_stacked_2_10.png Graphic file (type png)
<use ../analysis_results/classification/federated/client_distributions/client_s
hare_stacked_2_10.png>
Package pdftex.def Info: ../analysis_results/classification/federated/client_di
stributions/client_share_stacked_2_10.png  used on input line 584.
(pdftex.def)             Requested size: 247.6778pt x 122.52097pt.
 [7 <../plots/generation/bleu4_vs_clients_combined.png> <../analysis_results/cl
assification/federated/client_distributions/overall_class_distribution.png> <..
/analysis_results/classification/federated/client_distributions/client_share_st
acked_2_5.png> <../analysis_results/classification/federated/client_distributio
ns/client_share_stacked_2_10.png>]
File: ../plots/generation/rouge1_vs_clients_combined.png Graphic file (type png
)
<use ../plots/generation/rouge1_vs_clients_combined.png>
Package pdftex.def Info: ../plots/generation/rouge1_vs_clients_combined.png  us
ed on input line 615.
(pdftex.def)             Requested size: 247.6778pt x 135.35295pt.
File: ../plots/generation/bleu4_vs_clients_combined.png Graphic file (type png)

<use ../plots/generation/bleu4_vs_clients_combined.png>
Package pdftex.def Info: ../plots/generation/bleu4_vs_clients_combined.png  use
d on input line 620.
(pdftex.def)             Requested size: 247.6778pt x 135.4008pt.

Underfull \vbox (badness 1231) has occurred while \output is active []


Underfull \hbox (badness 1867) in paragraph at lines 640--641
\OT1/ptm/m/n/10 ing degra-da-tion [15], both BART-large and Dis-til-BART
 []


Underfull \hbox (badness 2050) in paragraph at lines 642--643
\OT1/ptm/m/n/10 a con-sis-tent 1.0% ac-cu-racy ad-van-tage over Dis-til-BART
 []

[8]

** Conference Paper **
Before submitting the final camera ready copy, remember to:

 1. Manually equalize the lengths of two columns on the last page
 of your paper;

 2. Ensure that any PostScript and/or PDF output post-processing
 uses only Type 1 fonts and that every step in the generation
 process uses the appropriate paper size.

[9] [10] (./conference_101719.aux) ) 
Here is how much of TeX's memory you used:
 4411 strings out of 478287
 72295 string characters out of 5849289
 399265 words of memory out of 5000000
 22541 multiletter control sequences out of 15000+600000
 522112 words of font info for 131 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 55i,11n,62p,1109b,342s stack positions out of 5000i,500n,10000p,200000b,80000s
{/usr/share/texlive/texmf-dist/fonts/enc/dv
ips/base/8r.enc}</usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/c
mmi10.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi8.p
fb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi9.pfb></us
r/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmr10.pfb></usr/share
/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmr7.pfb></usr/share/texlive
/texmf-dist/fonts/type1/public/amsfonts/cm/cmr8.pfb></usr/share/texlive/texmf-d
ist/fonts/type1/public/amsfonts/cm/cmr9.pfb></usr/share/texlive/texmf-dist/font
s/type1/public/amsfonts/cm/cmsy10.pfb></usr/share/texlive/texmf-dist/fonts/type
1/public/amsfonts/cm/cmsy7.pfb></usr/share/texlive/texmf-dist/fonts/type1/publi
c/amsfonts/cm/cmsy8.pfb></usr/share/texlive/texmf-dist/fonts/type1/urw/courier/
ucrr8a.pfb></usr/share/texlive/texmf-dist/fonts/type1/urw/times/utmb8a.pfb></us
r/share/texlive/texmf-dist/fonts/type1/urw/times/utmbi8a.pfb></usr/share/texliv
e/texmf-dist/fonts/type1/urw/times/utmr8a.pfb></usr/share/texlive/texmf-dist/fo
nts/type1/urw/times/utmri8a.pfb>
Output written on conference_101719.pdf (10 pages, 1734947 bytes).
PDF statistics:
 136 PDF objects out of 1000 (max. 8388607)
 71 compressed objects within 1 object stream
 0 named destinations out of 1000 (max. 500000)
 56 words of extra memory for PDF output out of 10000 (max. 10000000)

